# 镜头028 - 修改代码特写

## 📋 基本信息
- **镜头编号**: 028
- **序列**: 现实的落差
- **时间码**: 00:01:38 - 00:01:42
- **景别**: 特写
- **时长**: 4秒

## 🎬 镜头内容
屏幕上的代码，小李按老板思路修改，努力的尝试

## 📸 摄影技术规格

### 摄影机设置
- **摄影机**: Sony FX6 / Canon C70
- **镜头**: 100mm微距镜头
- **光圈**: f/4.0
- **快门速度**: 1/50s (25fps)
- **ISO**: 400
- **白平衡**: 6500K (显示器色温)

### 构图与角度
- **拍摄角度**: 垂直于屏幕
- **构图**: 屏幕占满画面，代码修改过程可见
- **焦点**: 对焦在正在修改的代码行
- **景深**: 中等景深，保持代码可读

### 光线设计
- **主光源**: 显示器自发光
- **辅光**: 办公室照明作为环境光
- **光比**: 显示器亮度为主
- **色温**: 6500K，保持屏幕色彩准确

### 运动设计
- **机位**: 固定机位
- **运动**: 无摄影机运动
- **稳定**: 三脚架固定
- **屏幕录制**: 同时进行屏幕录制

## 🔊 音频要求
- **主要音频**: 专注的键盘声
- **环境音**: 办公室环境音
- **录音方式**: 现场环境录音

## 🎨 后期处理指导
- **调色**: 保持屏幕色彩真实
- **对比度**: 适度提高，突出代码修改
- **锐化**: 确保代码文字清晰
- **去摩尔纹**: 处理可能的屏幕摩尔纹

## ⚠️ 拍摄注意事项
- 准备真实的代码修改内容
- 避免屏幕反光和摩尔纹
- 确保修改过程逻辑合理
- 控制曝光，避免屏幕过亮

## 📝 备用方案
- **Plan B**: 使用偏振镜减少反光
- **Plan C**: 调整屏幕亮度和对比度
- **屏幕录制**: 同时录制屏幕内容作为备用

## 🎯 情感表达目标
通过专注的代码修改过程，展现主角按照老板思路努力尝试的认真态度和积极行动。
