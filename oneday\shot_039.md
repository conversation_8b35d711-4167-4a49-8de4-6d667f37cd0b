# 镜头039 - 分屏浏览特写

## 📋 基本信息
- **镜头编号**: 039
- **序列**: 摸鱼时光
- **时间码**: 00:02:13 - 00:02:19
- **景别**: 特写
- **时长**: 6秒

## 🎬 镜头内容
屏幕分屏：一边AI修代码，一边浏览Midjourney，工作与娱乐的并行

## 📸 摄影技术规格

### 摄影机设置
- **摄影机**: Sony FX6 / Canon C70
- **镜头**: 100mm微距镜头
- **光圈**: f/4.0
- **快门速度**: 1/50s (25fps)
- **ISO**: 400
- **白平衡**: 6500K (显示器色温)

### 构图与角度
- **拍摄角度**: 垂直于屏幕
- **构图**: 屏幕占满画面，分屏效果清晰
- **焦点**: 对焦在屏幕中央，两边内容都清晰
- **景深**: 中等景深，保持屏幕内容可读

### 光线设计
- **主光源**: 显示器自发光
- **辅光**: 办公室照明作为环境光
- **光比**: 显示器亮度为主
- **色温**: 6500K，保持屏幕色彩准确

### 运动设计
- **机位**: 固定机位
- **运动**: 无摄影机运动
- **稳定**: 三脚架固定
- **屏幕录制**: 同时进行屏幕录制

## 🔊 音频要求
- **主要音频**: 键盘声，网页加载声
- **环境音**: 办公室环境音
- **录音方式**: 现场环境录音

## 🎨 后期处理指导
- **调色**: 保持屏幕色彩真实
- **对比度**: 适度提高，突出分屏效果
- **锐化**: 确保两边内容都清晰
- **分屏强调**: 可添加分割线强调分屏效果

## ⚠️ 拍摄注意事项
- 准备真实的AI修复界面和Midjourney网站
- 避免屏幕反光和摩尔纹
- 确保分屏内容都有意义
- 控制曝光，避免屏幕过亮

## 📝 备用方案
- **Plan B**: 使用偏振镜减少反光
- **Plan C**: 后期合成分屏效果
- **屏幕录制**: 同时录制屏幕内容作为备用

## 🎯 情感表达目标
通过分屏显示，直观展现现代职场人工作时的分心状态，体现工作效率的下降和内心的不专注。

---

## 🎨 **美术指导 - 视觉设计规范**

### 📐 分屏界面设计

#### **左侧：AI修复工具界面**
```
┌─ GitHub Copilot ─────────────────┐
│ 🤖 AI Code Assistant            │
│                                  │
│ Fixing bugs in UserProfile.jsx  │
│ ⏳ Processing... 67%             │
│                                  │
│ Suggested fixes:                 │
│ ✓ Add null check for user data  │
│ ✓ Fix async/await syntax         │
│ ⏳ Optimizing performance...     │
│                                  │
│ [Apply All] [Review] [Cancel]    │
└──────────────────────────────────┘
```

#### **右侧：Midjourney网站界面**
```
┌─ Midjourney Gallery ────────────┐
│ 🎨 AI Art Generation            │
│                                  │
│ [Featured Artwork]               │
│ ┌─────┐ ┌─────┐ ┌─────┐         │
│ │ 🌅  │ │ 🏙️  │ │ 🎭  │         │
│ │Art1 │ │Art2 │ │Art3 │         │
│ └─────┘ └─────┘ └─────┘         │
│                                  │
│ "Cyberpunk cityscape at sunset" │
│ 👍 2.3k  💬 156  🔄 89          │
└──────────────────────────────────┘
```

#### **分屏视觉设计**
- **分割线**：细微的垂直分割线，不抢夺视觉焦点
- **左右平衡**：50:50分屏，体现注意力的分散
- **色彩对比**：左侧偏冷色调(工作)，右侧偏暖色调(娱乐)
- **内容层次**：右侧内容更吸引眼球，体现分心状态

### 🎭 现代职场真实感设计

#### **AI工具界面真实性**
- **品牌选择**：使用真实的AI编程助手界面
- **功能展示**：显示真实的代码修复过程
- **进度显示**：动态进度条，增强真实感
- **交互元素**：真实的按钮和选项设计

#### **娱乐网站设计**
- **内容选择**：精美的AI艺术作品展示
- **社交元素**：点赞、评论、分享数据
- **视觉吸引**：色彩丰富的艺术作品
- **对比效果**：与左侧工作内容形成强烈对比

### 🔧 技术实现标准

#### **屏幕录制要求**
- **同步录制**：左右两侧内容同步操作
- **真实交互**：鼠标在两侧间真实切换
- **时间控制**：6秒内展现完整的分心过程
- **质量标准**：确保分屏内容都清晰可见
